import express, { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import winston from 'winston';
import path from 'path';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { connectDatabase, disconnectDatabase } from './config/database';
import cookieParser from 'cookie-parser';
import websocketService from './services/websocket';
import mediaCleanupService from './services/media-cleanup';

// Initialize environment variables
dotenv.config();

// Import routes
import authRouter from './controllers/auth.controller';
import userRouter from './controllers/user.controller';
import adminRouter from './routes/admin.routes';

// Import new API routes
import chatRouter from './api/chat';
//import mediaRouter from './api/media';
//import callsRouter from './api/calls';
//import deviceRouter from './api/device';
import buildsRouter from './api/builds';
import systemRouter from './api/system';
//import auditRouter from './api/audit';
//import expressionsRouter from './api/expressions';
//import configRouter from './api/config';
//import voiceRouter from './api/voice';
//import debugRouter from './api/debug';

// Import middleware
import errorHandler from './middleware/errorHandler';
import { adminPanelOnly, mobileAppOnly, blockWebAccessForUsers, verifyAdminOnlyEndpoint } from './middleware/strictAuth';

// Logger setup
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'ccalc-backend' },
  transports: [
    new winston.transports.File({ filename: './logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: './logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

const app = express();
const PORT = process.env.PORT || 3000;

// Trust proxy for nginx reverse proxy
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com', 'http://localhost:3001', 'http://frontend:3001']
    : [
        'http://localhost:3001', 
        'http://localhost:3005',  // Next.js dev server
        'http://localhost:3000', 
        'http://frontend:3001', 
        'http://127.0.0.1:3001',
        'http://127.0.0.1:3005',   // Next.js dev server
        'http://***************:8082',  // Expo app (new IP)
        'http://***************:3001',  // Frontend on network (new IP)
        'http://***************:3000',  // Backend on network (new IP)
        'exp://***************:8082',   // Expo protocol (new IP)
        'ccalc://localhost:8082'        // Custom scheme
      ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Cookie', 'Set-Cookie', 'X-Admin-Token', 'X-CSRF-Token'],
  exposedHeaders: ['Set-Cookie', 'X-Admin-Token']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { error: 'Too many requests from this IP, please try again later.' },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api', limiter);

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Request logging
app.use((req: Request, res: Response, next: NextFunction) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  next();
});

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'ccalc-backend',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    hostname: require('os').hostname()
  });
});

// Also add /api/health for consistency
app.get('/api/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'ccalc-backend',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    hostname: require('os').hostname()
  });
});

// CSRF token endpoint
app.get('/api/csrf-token', (req: Request, res: Response) => {
  res.json({ 
    csrfToken: 'dev-token-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9)
  });
});

// Dashboard stats endpoint (admin only)
app.get('/api/dashboard/stats', adminPanelOnly, async (req: Request, res: Response) => {
  try {
    // Mock dashboard stats for now - match frontend DashboardStats interface
    const stats = {
      totalUsers: 150,
      activeUsers: 120,
      lockedUsers: 30,
      totalAdmins: 8,
      totalBuilds: 25,
      systemHealth: 'OK',
      uptime: `${Math.floor(process.uptime() / 86400)}d ${Math.floor((process.uptime() % 86400) / 3600)}h ${Math.floor((process.uptime() % 3600) / 60)}m`,
      recentFailedLogins: 5,
      userGrowth: 12.5,
      serverLoad: Math.round(process.memoryUsage().heapUsed / 1024 / 1024)
    };
    
    res.json(stats);
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard stats' });
  }
});

// API routes with strict authentication
// Authentication routes (no additional middleware needed)
app.use('/api/auth', authRouter);

// Admin-only routes
app.use('/api/admin', adminPanelOnly, adminRouter);
//app.use('/api/audit', adminPanelOnly, auditRouter);
app.use('/api/system', adminPanelOnly, systemRouter);

// User routes (mobile app only)
app.use('/api/users', /*blockWebAccessForUsers, mobileAppOnly,*/ userRouter);
app.use('/api/chat', /*blockWebAccessForUsers, mobileAppOnly,*/ chatRouter);
//app.use('/api/calls', /*blockWebAccessForUsers, mobileAppOnly,*/ callsRouter);
//app.use('/api/device', /*blockWebAccessForUsers, mobileAppOnly,*/ deviceRouter);

// Mixed routes with path-based authentication
//app.use('/api/voice', verifyAdminOnlyEndpoint, voiceRouter);
//app.use('/api/media', verifyAdminOnlyEndpoint, mediaRouter);
app.use('/api/builds', adminPanelOnly, buildsRouter);
//app.use('/api/expressions', adminPanelOnly, expressionsRouter);
//app.use('/api/config', adminPanelOnly, configRouter);
//app.use('/api/debug', adminPanelOnly, debugRouter);

// Serve static files
app.use('/static', express.static(path.join(__dirname, 'public')));

// 404 handler for API routes - using string matching instead of wildcard
app.use((req: Request, res: Response, next: NextFunction) => {
  if (req.path.startsWith('/api/')) {
    res.status(404).json({ error: 'API endpoint not found' });
  } else {
    next();
  }
});

// Root endpoint
app.get('/', (req: Request, res: Response) => {  res.json({
    message: 'CCALC Backend API',
    version: '1.0.0',
    status: 'running',
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      users: '/api/users',
      admin: '/api/admin',
      chat: '/api/chat',
      media: '/api/media',
      calls: '/api/calls',
      device: '/api/device',
      builds: '/api/builds',
      system: '/api/system'
    }
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await disconnectDatabase();
  process.exit(0);
});

// Start server with WebSocket support
async function startServer() {
  try {
    await connectDatabase();

    // Create HTTP server for WebSocket support
    const server = createServer(app);

    // Initialize WebSocket service for real-time messaging
    websocketService.initialize(server);

    // Initialize media cleanup service
    mediaCleanupService.initialize();

    server.listen(Number(PORT), '0.0.0.0', () => {
      logger.info(`CCALC Backend server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`Health check: http://localhost:${PORT}/health`);
      logger.info(`WebSocket endpoint: ws://localhost:${PORT}/ws/chat`);
      logger.info(`Media cleanup service: Automated cleanup enabled`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
