/**
 * Clean & Minimal Attachment Picker
 * Gallery, Documents, Audio Files only (NO voice notes)
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as Haptics from 'expo-haptics';
import { theme } from '../utils/theme';
import IsolatedMediaService from '../services/IsolatedMediaService';
import { MediaAttachment } from '../services/MediaService';

export interface AttachmentOption {
  id: string;
  title: string;
  color: string;
}

export interface AttachmentPickerProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (attachment: any) => void;
}

const { width } = Dimensions.get('window');

export const AttachmentPicker: React.FC<AttachmentPickerProps> = ({
  visible,
  onClose,
  onSelect,
}) => {
  const slideAnim = React.useRef(new Animated.Value(300)).current;
  const isolatedMediaService = React.useRef(new IsolatedMediaService()).current;

  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 65,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Clean, minimal options with isolated media - NO voice notes
  const attachmentOptions: AttachmentOption[] = [
    {
      id: 'gallery',
      title: 'Photos & Videos (Limited)',
      color: '#007AFF',
    },
    {
      id: 'camera',
      title: 'Camera',
      color: '#34C759',
    },
    {
      id: 'document',
      title: 'Documents',
      color: '#FF3B30',
    },
    {
      id: 'audio',
      title: 'Audio Files',
      color: '#FF9500',
    },
  ];

  const handleOptionPress = async (option: AttachmentOption) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    try {
      switch (option.id) {
        case 'gallery':
          // Use isolated media service for limited gallery access (Instagram-like)
          const galleryAttachment = await isolatedMediaService.selectFromLibrary({
            allowsEditing: false,
            quality: 0.8,
            mediaTypes: 'all',
          });

          if (galleryAttachment) {
            onSelect(galleryAttachment);
            onClose();
          }
          return;

        case 'camera':
          // Use isolated media service for camera
          const cameraAttachment = await isolatedMediaService.takePhoto({
            allowsEditing: true,
            quality: 0.8,
          });

          if (cameraAttachment) {
            onSelect(cameraAttachment);
            onClose();
          }
          return;

        case 'document':
          // Use isolated media service for documents
          const documentAttachment = await isolatedMediaService.selectDocument();

          if (documentAttachment) {
            onSelect(documentAttachment);
            onClose();
          }
          return;

        case 'audio':
          // Use document picker for audio files (no voice recording)
          const audioResult = await DocumentPicker.getDocumentAsync({
            type: [
              'audio/mpeg',
              'audio/wav',
              'audio/mp4',
              'audio/m4a',
              'audio/aac',
            ],
            copyToCacheDirectory: false,
            multiple: false,
          });

          if (!audioResult.canceled && audioResult.assets && audioResult.assets.length > 0) {
            const asset = audioResult.assets[0];
            const audioAttachment: MediaAttachment = {
              id: `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              name: asset.name,
              type: 'audio',
              size: asset.size || 0,
              uri: asset.uri,
              mimeType: asset.mimeType || 'audio/mpeg',
              isImage: false,
              isVideo: false,
              isAudio: true,
            };

            onSelect(audioAttachment);
            onClose();
          }
          return;

        default:
          return;
      }
    } catch (error) {
      console.error('Attachment picker error:', error);
      alert('Failed to select attachment');
    }
  };

  const getFileCategory = (mimeType: string): string => {
    if (mimeType.startsWith('application/pdf')) return 'PDF';
    if (mimeType.startsWith('application/msword') || 
        mimeType.includes('wordprocessingml')) return 'Document';
    if (mimeType.startsWith('application/vnd.ms-excel') || 
        mimeType.includes('spreadsheetml')) return 'Spreadsheet';
    if (mimeType.startsWith('text/')) return 'Text File';
    if (mimeType.startsWith('audio/')) return 'Audio';
    if (mimeType.startsWith('video/')) return 'Video';
    if (mimeType.startsWith('image/')) return 'Image';
    return 'File';
  };

  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={onClose}
        />
        
        <Animated.View 
          style={[
            styles.container,
            { transform: [{ translateY: slideAnim }] }
          ]}
        >
          <View style={styles.handle} />
          
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Share</Text>
          </View>

          <View style={styles.content}>
            {attachmentOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[styles.option, { borderLeftColor: option.color }]}
                onPress={() => handleOptionPress(option)}
                activeOpacity={0.7}
              >
                <Text style={styles.optionTitle}>{option.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  container: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 20,
  },
  handle: {
    width: 32,
    height: 4,
    backgroundColor: '#C7C7CC',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: theme.colors.label,
    textAlign: 'center',
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 12,
  },
  option: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderLeftWidth: 4,
    marginBottom: 8,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.label,
  },
});
